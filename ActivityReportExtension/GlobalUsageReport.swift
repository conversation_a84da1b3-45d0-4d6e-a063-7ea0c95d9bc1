//
//  GlobalUsageReport.swift
//  com.redpill.client.ios.report
//

import SwiftUI
import FamilyControls
import DeviceActivity
import os.log

@preconcurrency
struct GlobalUsageReport: DeviceActivityReportScene {
    
    let context = DeviceActivityReport.Context("GlobalUsage")
    
    // The view is rendered in the main app, but we don't need to show anything.
    // Make it nonisolated and use @Sendable to ensure it's safe for concurrency
    nonisolated let content: @Sendable (String) -> EmptyView = { _ in EmptyView() }

    nonisolated func makeConfiguration(
        representing data: DeviceActivityResults<DeviceActivityData>
    ) async -> String {
        let log = Logger(
            subsystem: "com.redpill.client.ios.report",
            category: "GlobalUsageReport"
        )
        log.info("🎯 GlobalUsageReport.makeConfiguration called - starting real app usage capture")
        log.info("📱 This extension captures device-wide app usage data")
        
        // Don't load the full store in extensions - use minimal memory
        var recordsProcessed = 0
        var batchRecords: [ActivityRecord] = []
        let batchSize = 10 // Much smaller batches for extensions

        // Create an iterator for the AsyncSequence and immediately use it
        let iterator = data.makeAsyncIterator()

        // Process each element in the sequence
        while let activityData = await iterator.next() {
            log.info("Processing activity data")

            // Process activity segments (new API structure)
            for await activitySegment in activityData.activitySegments {
                log.info("Processing activity segment with duration: \(activitySegment.totalActivityDuration)")

                // Process categories within the segment
                for await categoryActivity in activitySegment.categories {
                    let categoryIdentifier = categoryActivity.category.localizedDisplayName ?? "Unknown Category"
                    let duration = categoryActivity.totalActivityDuration

                    if duration > 0 {
                        let now = Date()
                        let beginMs = Int64((now.timeIntervalSince1970 - duration) * 1000)
                        let endMs = Int64(now.timeIntervalSince1970 * 1000)

                        let record = ActivityRecord(
                            source: "category:\(categoryIdentifier)",
                            subtype: "category.usage",
                            begin: beginMs,
                            end: endMs,
                            userTimezone: TimeZone.current.secondsFromGMT() / 60, // Convert to minutes
                            workingIntensity: 1.0
                        )

                        batchRecords.append(record)
                        recordsProcessed += 1

                        // Process in batches to reduce memory usage
                        if batchRecords.count >= batchSize {
                            await ActivityStore.shared.append(contentsOf: batchRecords)
                            batchRecords.removeAll()
                            // Force memory cleanup
                            autoreleasepool { }
                        }

                        log.info("Created record for category \(categoryIdentifier)")
                    }

                    // Process applications within the category
                    for await appActivity in categoryActivity.applications {
                        let bundleIdentifier = appActivity.application.bundleIdentifier ?? "unknown.app"
                        // Avoid LaunchServices calls that cause permission errors
                        let appName = bundleIdentifier.components(separatedBy: ".").last ?? bundleIdentifier
                        let duration = appActivity.totalActivityDuration

                        if duration > 0 {
                            let now = Date()
                            let beginMs = Int64((now.timeIntervalSince1970 - duration) * 1000)
                            let endMs = Int64(now.timeIntervalSince1970 * 1000)

                            let record = ActivityRecord(
                                source: bundleIdentifier,
                                subtype: appName,
                                begin: beginMs,
                                end: endMs,
                                userTimezone: TimeZone.current.secondsFromGMT() / 60, // Convert to minutes
                                workingIntensity: 1.0
                            )

                            batchRecords.append(record)
                            recordsProcessed += 1

                            // Process in batches to reduce memory usage
                            if batchRecords.count >= batchSize {
                                await ActivityStore.shared.append(contentsOf: batchRecords)
                                batchRecords.removeAll()
                                // Force memory cleanup
                                autoreleasepool { }
                            }

                            log.info("✅ Created record: \(bundleIdentifier) (\(appName)) - \(duration)s")
                        }
                    }
                }
            }
        }

        // Save any remaining records in the final batch
        if !batchRecords.isEmpty {
            await ActivityStore.shared.append(contentsOf: batchRecords)
            batchRecords.removeAll()
        }

        log.info("🎉 GlobalUsageReport: Processing complete. \(recordsProcessed) real app usage records were saved.")

        // Force final memory cleanup
        autoreleasepool { }

        // Return empty string since we're processing data in batches and saving directly
        return ""
    }
}
