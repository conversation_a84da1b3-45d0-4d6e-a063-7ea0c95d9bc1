// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 90;
	objects = {

/* Begin PBXBuildFile section */
		612E82452E005EB20024491F /* DeviceActivity.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6178D1CF2DE467F80053D9CA /* DeviceActivity.framework */; };
		612E82492E0063600024491F /* FamilyControls.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 612E82482E0063600024491F /* FamilyControls.framework */; };
		612E824A2E0063700024491F /* FamilyControls.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 612E82482E0063600024491F /* FamilyControls.framework */; };
		6178D1912DE31DA80053D9CA /* FirebaseAnalytics in Frameworks */ = {isa = PBXBuildFile; productRef = 6178D1902DE31DA80053D9CA /* FirebaseAnalytics */; };
		6178D1932DE31DA80053D9CA /* FirebaseAppCheck in Frameworks */ = {isa = PBXBuildFile; productRef = 6178D1922DE31DA80053D9CA /* FirebaseAppCheck */; };
		6178D1952DE31DA80053D9CA /* FirebaseAuth in Frameworks */ = {isa = PBXBuildFile; productRef = 6178D1942DE31DA80053D9CA /* FirebaseAuth */; };
		6178D1972DE31DA80053D9CA /* FirebaseCore in Frameworks */ = {isa = PBXBuildFile; productRef = 6178D1962DE31DA80053D9CA /* FirebaseCore */; };
		6178D1992DE31DA80053D9CA /* FirebaseFirestore in Frameworks */ = {isa = PBXBuildFile; productRef = 6178D1982DE31DA80053D9CA /* FirebaseFirestore */; };
		6178D1C42DE320D40053D9CA /* GoogleSignIn in Frameworks */ = {isa = PBXBuildFile; productRef = 6178D1C32DE320D40053D9CA /* GoogleSignIn */; };
		6178D1C62DE320D40053D9CA /* GoogleSignInSwift in Frameworks */ = {isa = PBXBuildFile; productRef = 6178D1C52DE320D40053D9CA /* GoogleSignInSwift */; };
		6178D1E32DE468300053D9CA /* DeviceActivity.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6178D1CF2DE467F80053D9CA /* DeviceActivity.framework */; };
		617CD7AB2E002ED3005A1360 /* DeviceActivity.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 6178D1CF2DE467F80053D9CA /* DeviceActivity.framework */; };
		61C677F82DFEE89A001E7DC8 /* ActivityReportExtension.appex in Embed ExtensionKit Extensions */ = {isa = PBXBuildFile; fileRef = 6178D1F42DE468CC0053D9CA /* ActivityReportExtension.appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		61C677FC2DFEE8E9001E7DC8 /* ActivityMonitorExtension.appex in Embed Foundation Extensions */ = {isa = PBXBuildFile; fileRef = 6178D1E22DE468300053D9CA /* ActivityMonitorExtension.appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		61C677F92DFEE89A001E7DC8 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 6178D1792DE31C9B0053D9CA /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 6178D1F32DE468CC0053D9CA;
			remoteInfo = ActivityReportExtension;
		};
		61C677FD2DFEE8E9001E7DC8 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 6178D1792DE31C9B0053D9CA /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 6178D1E12DE468300053D9CA;
			remoteInfo = ActivityMonitorExtension;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		61C677FB2DFEE89A001E7DC8 /* Embed ExtensionKit Extensions */ = {
			isa = PBXCopyFilesBuildPhase;
			dstPath = "$(EXTENSIONS_FOLDER_PATH)";
			dstSubfolder = Product;
			files = (
				61C677F82DFEE89A001E7DC8 /* ActivityReportExtension.appex in Embed ExtensionKit Extensions */,
			);
			name = "Embed ExtensionKit Extensions";
		};
		61C677FF2DFEE8E9001E7DC8 /* Embed Foundation Extensions */ = {
			isa = PBXCopyFilesBuildPhase;
			dstPath = "";
			dstSubfolder = PlugIns;
			files = (
				61C677FC2DFEE8E9001E7DC8 /* ActivityMonitorExtension.appex in Embed Foundation Extensions */,
			);
			name = "Embed Foundation Extensions";
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		612E82482E0063600024491F /* FamilyControls.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = FamilyControls.framework; path = System/Library/Frameworks/FamilyControls.framework; sourceTree = SDKROOT; };
		6178D1812DE31C9B0053D9CA /* redpill.client.ios.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = redpill.client.ios.app; sourceTree = BUILT_PRODUCTS_DIR; };
		6178D1CF2DE467F80053D9CA /* DeviceActivity.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = DeviceActivity.framework; path = System/Library/Frameworks/DeviceActivity.framework; sourceTree = SDKROOT; };
		6178D1E22DE468300053D9CA /* ActivityMonitorExtension.appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = ActivityMonitorExtension.appex; sourceTree = BUILT_PRODUCTS_DIR; };
		6178D1F42DE468CC0053D9CA /* ActivityReportExtension.appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.extensionkit-extension"; includeInIndex = 0; path = ActivityReportExtension.appex; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		6178D2012DE468CC0053D9CA /* Exceptions for "ActivityReportExtension" folder in "ActivityReportExtension" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = 6178D1F32DE468CC0053D9CA /* ActivityReportExtension */;
		};
		6178D2082DE469850053D9CA /* Exceptions for "redpill.client.ios" folder in "redpill.client.ios" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				info.plist,
			);
			target = 6178D1802DE31C9B0053D9CA /* redpill.client.ios */;
		};
		61AD4B4B2DF6E26700962ADE /* Exceptions for "redpill.client.ios" folder in "ActivityMonitorExtension" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Core/ActivityMonitor.swift,
				Models/ActivityRecord.swift,
				"Models/ActivityRecord+Extensions.swift",
				Models/ActivityStore.swift,
				"Models/ActivityStore+Aggregate.swift",
				"Models/ActivityStore+Logging.swift",
				Services/AppLogger.swift,
				SharedConstants.swift,
			);
			target = 6178D1E12DE468300053D9CA /* ActivityMonitorExtension */;
		};
		61AD4B4C2DF6E26700962ADE /* Exceptions for "redpill.client.ios" folder in "ActivityReportExtension" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Core/ActivityMonitor.swift,
				DeviceActivityReportContext.swift,
				Models/ActivityRecord.swift,
				"Models/ActivityRecord+Extensions.swift",
				Models/ActivityStore.swift,
				"Models/ActivityStore+Aggregate.swift",
				"Models/ActivityStore+Logging.swift",
				Services/AppLogger.swift,
				SharedConstants.swift,
				UI/TotalActivityView.swift,
			);
			target = 6178D1F32DE468CC0053D9CA /* ActivityReportExtension */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		6178D1832DE31C9B0053D9CA /* redpill.client.ios */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				6178D2082DE469850053D9CA /* Exceptions for "redpill.client.ios" folder in "redpill.client.ios" target */,
				61AD4B4B2DF6E26700962ADE /* Exceptions for "redpill.client.ios" folder in "ActivityMonitorExtension" target */,
				61AD4B4C2DF6E26700962ADE /* Exceptions for "redpill.client.ios" folder in "ActivityReportExtension" target */,
			);
			path = redpill.client.ios;
			sourceTree = "<group>";
		};
		6178D1E42DE468300053D9CA /* ActivityMonitorExtension */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = ActivityMonitorExtension;
			sourceTree = "<group>";
		};
		6178D1F52DE468CC0053D9CA /* ActivityReportExtension */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				6178D2012DE468CC0053D9CA /* Exceptions for "ActivityReportExtension" folder in "ActivityReportExtension" target */,
			);
			path = ActivityReportExtension;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		6178D17E2DE31C9B0053D9CA /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			files = (
				6178D1932DE31DA80053D9CA /* FirebaseAppCheck in Frameworks */,
				612E82492E0063600024491F /* FamilyControls.framework in Frameworks */,
				612E82452E005EB20024491F /* DeviceActivity.framework in Frameworks */,
				6178D1952DE31DA80053D9CA /* FirebaseAuth in Frameworks */,
				6178D1C62DE320D40053D9CA /* GoogleSignInSwift in Frameworks */,
				6178D1972DE31DA80053D9CA /* FirebaseCore in Frameworks */,
				6178D1992DE31DA80053D9CA /* FirebaseFirestore in Frameworks */,
				6178D1912DE31DA80053D9CA /* FirebaseAnalytics in Frameworks */,
				6178D1C42DE320D40053D9CA /* GoogleSignIn in Frameworks */,
			);
		};
		6178D1DF2DE468300053D9CA /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			files = (
				612E824A2E0063700024491F /* FamilyControls.framework in Frameworks */,
				6178D1E32DE468300053D9CA /* DeviceActivity.framework in Frameworks */,
			);
		};
		6178D1F12DE468CC0053D9CA /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			files = (
				617CD7AB2E002ED3005A1360 /* DeviceActivity.framework in Frameworks */,
			);
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		6178D1782DE31C9B0053D9CA = {
			isa = PBXGroup;
			children = (
				6178D1832DE31C9B0053D9CA /* redpill.client.ios */,
				6178D1E42DE468300053D9CA /* ActivityMonitorExtension */,
				6178D1F52DE468CC0053D9CA /* ActivityReportExtension */,
				6178D1CE2DE467F80053D9CA /* Frameworks */,
				6178D1822DE31C9B0053D9CA /* Products */,
			);
			sourceTree = "<group>";
		};
		6178D1822DE31C9B0053D9CA /* Products */ = {
			isa = PBXGroup;
			children = (
				6178D1812DE31C9B0053D9CA /* redpill.client.ios.app */,
				6178D1E22DE468300053D9CA /* ActivityMonitorExtension.appex */,
				6178D1F42DE468CC0053D9CA /* ActivityReportExtension.appex */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		6178D1CE2DE467F80053D9CA /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				612E82482E0063600024491F /* FamilyControls.framework */,
				6178D1CF2DE467F80053D9CA /* DeviceActivity.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		6178D1802DE31C9B0053D9CA /* redpill.client.ios */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 6178D18C2DE31C9D0053D9CA /* Build configuration list for PBXNativeTarget "redpill.client.ios" */;
			buildPhases = (
				6178D17D2DE31C9B0053D9CA /* Sources */,
				6178D17E2DE31C9B0053D9CA /* Frameworks */,
				6178D17F2DE31C9B0053D9CA /* Resources */,
				61C677FB2DFEE89A001E7DC8 /* Embed ExtensionKit Extensions */,
				61C677FF2DFEE8E9001E7DC8 /* Embed Foundation Extensions */,
			);
			buildRules = (
			);
			dependencies = (
				61C677FA2DFEE89A001E7DC8 /* PBXTargetDependency */,
				61C677FE2DFEE8E9001E7DC8 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				6178D1832DE31C9B0053D9CA /* redpill.client.ios */,
			);
			name = redpill.client.ios;
			packageProductDependencies = (
				6178D1902DE31DA80053D9CA /* FirebaseAnalytics */,
				6178D1922DE31DA80053D9CA /* FirebaseAppCheck */,
				6178D1942DE31DA80053D9CA /* FirebaseAuth */,
				6178D1962DE31DA80053D9CA /* FirebaseCore */,
				6178D1982DE31DA80053D9CA /* FirebaseFirestore */,
				6178D1C32DE320D40053D9CA /* GoogleSignIn */,
				6178D1C52DE320D40053D9CA /* GoogleSignInSwift */,
			);
			productName = redpill.client.ios;
			productReference = 6178D1812DE31C9B0053D9CA /* redpill.client.ios.app */;
			productType = "com.apple.product-type.application";
		};
		6178D1E12DE468300053D9CA /* ActivityMonitorExtension */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 6178D1ED2DE468300053D9CA /* Build configuration list for PBXNativeTarget "ActivityMonitorExtension" */;
			buildPhases = (
				6178D1DE2DE468300053D9CA /* Sources */,
				6178D1DF2DE468300053D9CA /* Frameworks */,
			);
			buildRules = (
			);
			fileSystemSynchronizedGroups = (
				6178D1E42DE468300053D9CA /* ActivityMonitorExtension */,
			);
			name = ActivityMonitorExtension;
			productName = MyMonitorExtension;
			productReference = 6178D1E22DE468300053D9CA /* ActivityMonitorExtension.appex */;
			productType = "com.apple.product-type.app-extension";
		};
		6178D1F32DE468CC0053D9CA /* ActivityReportExtension */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 6178D2022DE468CC0053D9CA /* Build configuration list for PBXNativeTarget "ActivityReportExtension" */;
			buildPhases = (
				6178D1F02DE468CC0053D9CA /* Sources */,
				6178D1F12DE468CC0053D9CA /* Frameworks */,
				6178D1F22DE468CC0053D9CA /* Resources */,
			);
			buildRules = (
			);
			fileSystemSynchronizedGroups = (
				6178D1F52DE468CC0053D9CA /* ActivityReportExtension */,
			);
			name = ActivityReportExtension;
			productName = ActivityReportExtension;
			productReference = 6178D1F42DE468CC0053D9CA /* ActivityReportExtension.appex */;
			productType = "com.apple.product-type.extensionkit-extension";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		6178D1792DE31C9B0053D9CA /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1630;
				LastUpgradeCheck = 1630;
				TargetAttributes = {
					6178D1802DE31C9B0053D9CA = {
						CreatedOnToolsVersion = 16.3;
					};
					6178D1E12DE468300053D9CA = {
						CreatedOnToolsVersion = 16.3;
					};
					6178D1F32DE468CC0053D9CA = {
						CreatedOnToolsVersion = 16.3;
					};
				};
			};
			buildConfigurationList = 6178D17C2DE31C9B0053D9CA /* Build configuration list for PBXProject "redpill.client.ios" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 6178D1782DE31C9B0053D9CA;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				6178D18F2DE31DA80053D9CA /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */,
				6178D1C22DE320D40053D9CA /* XCRemoteSwiftPackageReference "GoogleSignIn-iOS" */,
			);
			preferredProjectObjectVersion = 90;
			productRefGroup = 6178D1822DE31C9B0053D9CA /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				6178D1802DE31C9B0053D9CA /* redpill.client.ios */,
				6178D1E12DE468300053D9CA /* ActivityMonitorExtension */,
				6178D1F32DE468CC0053D9CA /* ActivityReportExtension */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		6178D17F2DE31C9B0053D9CA /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			files = (
			);
		};
		6178D1F22DE468CC0053D9CA /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			files = (
			);
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		6178D17D2DE31C9B0053D9CA /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			files = (
			);
		};
		6178D1DE2DE468300053D9CA /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			files = (
			);
		};
		6178D1F02DE468CC0053D9CA /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			files = (
			);
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		61C677FA2DFEE89A001E7DC8 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 6178D1F32DE468CC0053D9CA /* ActivityReportExtension */;
			targetProxy = 61C677F92DFEE89A001E7DC8 /* PBXContainerItemProxy */;
		};
		61C677FE2DFEE8E9001E7DC8 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 6178D1E12DE468300053D9CA /* ActivityMonitorExtension */;
			targetProxy = 61C677FD2DFEE8E9001E7DC8 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		6178D18A2DE31C9D0053D9CA /* Debug configuration for PBXProject "redpill.client.ios" */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				DEVELOPMENT_TEAM = 5GAG9VC98C;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		6178D18B2DE31C9D0053D9CA /* Release configuration for PBXProject "redpill.client.ios" */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				DEVELOPMENT_TEAM = 5GAG9VC98C;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		6178D18D2DE31C9D0053D9CA /* Debug configuration for PBXNativeTarget "redpill.client.ios" */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = redpill.client.ios/redpill.client.ios.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = BUH97A4T64;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "redpill-client-ios-Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = PravaApp;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.productivity";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 18.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				OTHER_SWIFT_FLAGS = "-DAPP_TARGET";
				PRODUCT_BUNDLE_IDENTIFIER = com.redpill.client.ios;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 6.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		6178D18E2DE31C9D0053D9CA /* Release configuration for PBXNativeTarget "redpill.client.ios" */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = redpill.client.ios/redpill.client.ios.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = BUH97A4T64;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = "redpill-client-ios-Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = PravaApp;
				INFOPLIST_KEY_LSApplicationCategoryType = "public.app-category.productivity";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				IPHONEOS_DEPLOYMENT_TARGET = 18.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				OTHER_SWIFT_FLAGS = "-DAPP_TARGET";
				PRODUCT_BUNDLE_IDENTIFIER = com.redpill.client.ios;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 6.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		6178D1EE2DE468300053D9CA /* Debug configuration for PBXNativeTarget "ActivityMonitorExtension" */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_ENTITLEMENTS = ActivityMonitorExtension/ActivityMonitorExtension.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = BUH97A4T64;
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "$(PROJECT_DIR)/ActivityMonitorExtension/Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = ActivityMonitorExtension;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 18.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.redpill.client.ios.monitor;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = MONITOR_EXTENSION;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 6.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		6178D1EF2DE468300053D9CA /* Release configuration for PBXNativeTarget "ActivityMonitorExtension" */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_ENTITLEMENTS = ActivityMonitorExtension/ActivityMonitorExtension.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = BUH97A4T64;
				GENERATE_INFOPLIST_FILE = NO;
				INFOPLIST_FILE = "$(PROJECT_DIR)/ActivityMonitorExtension/Info.plist";
				INFOPLIST_KEY_CFBundleDisplayName = ActivityMonitorExtension;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 18.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.redpill.client.ios.monitor;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SKIP_INSTALL = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = MONITOR_EXTENSION;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 6.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		6178D2032DE468CC0053D9CA /* Debug configuration for PBXNativeTarget "ActivityReportExtension" */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_ENTITLEMENTS = ActivityReportExtension/ActivityReportExtension.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = BUH97A4T64;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = ActivityReportExtension/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = ActivityReportExtension;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 18.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.redpill.client.ios.report;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 6.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		6178D2042DE468CC0053D9CA /* Release configuration for PBXNativeTarget "ActivityReportExtension" */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_ENTITLEMENTS = ActivityReportExtension/ActivityReportExtension.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = BUH97A4T64;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = ActivityReportExtension/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = ActivityReportExtension;
				INFOPLIST_KEY_NSHumanReadableCopyright = "";
				IPHONEOS_DEPLOYMENT_TARGET = 18.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.redpill.client.ios.report;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE_SPECIFIER = "";
				SKIP_INSTALL = YES;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 6.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		6178D17C2DE31C9B0053D9CA /* Build configuration list for PBXProject "redpill.client.ios" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				6178D18A2DE31C9D0053D9CA /* Debug configuration for PBXProject "redpill.client.ios" */,
				6178D18B2DE31C9D0053D9CA /* Release configuration for PBXProject "redpill.client.ios" */,
			);
			defaultConfigurationName = Release;
		};
		6178D18C2DE31C9D0053D9CA /* Build configuration list for PBXNativeTarget "redpill.client.ios" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				6178D18D2DE31C9D0053D9CA /* Debug configuration for PBXNativeTarget "redpill.client.ios" */,
				6178D18E2DE31C9D0053D9CA /* Release configuration for PBXNativeTarget "redpill.client.ios" */,
			);
			defaultConfigurationName = Release;
		};
		6178D1ED2DE468300053D9CA /* Build configuration list for PBXNativeTarget "ActivityMonitorExtension" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				6178D1EE2DE468300053D9CA /* Debug configuration for PBXNativeTarget "ActivityMonitorExtension" */,
				6178D1EF2DE468300053D9CA /* Release configuration for PBXNativeTarget "ActivityMonitorExtension" */,
			);
			defaultConfigurationName = Release;
		};
		6178D2022DE468CC0053D9CA /* Build configuration list for PBXNativeTarget "ActivityReportExtension" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				6178D2032DE468CC0053D9CA /* Debug configuration for PBXNativeTarget "ActivityReportExtension" */,
				6178D2042DE468CC0053D9CA /* Release configuration for PBXNativeTarget "ActivityReportExtension" */,
			);
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		6178D18F2DE31DA80053D9CA /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/firebase/firebase-ios-sdk.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 11.13.0;
			};
		};
		6178D1C22DE320D40053D9CA /* XCRemoteSwiftPackageReference "GoogleSignIn-iOS" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/google/GoogleSignIn-iOS";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 8.0.0;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		6178D1902DE31DA80053D9CA /* FirebaseAnalytics */ = {
			isa = XCSwiftPackageProductDependency;
			package = 6178D18F2DE31DA80053D9CA /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseAnalytics;
		};
		6178D1922DE31DA80053D9CA /* FirebaseAppCheck */ = {
			isa = XCSwiftPackageProductDependency;
			package = 6178D18F2DE31DA80053D9CA /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseAppCheck;
		};
		6178D1942DE31DA80053D9CA /* FirebaseAuth */ = {
			isa = XCSwiftPackageProductDependency;
			package = 6178D18F2DE31DA80053D9CA /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseAuth;
		};
		6178D1962DE31DA80053D9CA /* FirebaseCore */ = {
			isa = XCSwiftPackageProductDependency;
			package = 6178D18F2DE31DA80053D9CA /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseCore;
		};
		6178D1982DE31DA80053D9CA /* FirebaseFirestore */ = {
			isa = XCSwiftPackageProductDependency;
			package = 6178D18F2DE31DA80053D9CA /* XCRemoteSwiftPackageReference "firebase-ios-sdk" */;
			productName = FirebaseFirestore;
		};
		6178D1C32DE320D40053D9CA /* GoogleSignIn */ = {
			isa = XCSwiftPackageProductDependency;
			package = 6178D1C22DE320D40053D9CA /* XCRemoteSwiftPackageReference "GoogleSignIn-iOS" */;
			productName = GoogleSignIn;
		};
		6178D1C52DE320D40053D9CA /* GoogleSignInSwift */ = {
			isa = XCSwiftPackageProductDependency;
			package = 6178D1C22DE320D40053D9CA /* XCRemoteSwiftPackageReference "GoogleSignIn-iOS" */;
			productName = GoogleSignInSwift;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 6178D1792DE31C9B0053D9CA /* Project object */;
}
