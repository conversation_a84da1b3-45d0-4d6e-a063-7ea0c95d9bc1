//
//  ActivityMonitorExtension.swift
//  redpill.client.ios.monitor
//
//  This extension now correctly handles only the start and stop of monitoring.
//

// --- FIX: Add `@preconcurrency` to acknowledge non-Sendable types from this module ---
@preconcurrency import DeviceActivity
import Foundation
import os.log

class ActivityMonitorExtension: DeviceActivityMonitor {
    private let log = Logger(subsystem: "com.redpill.client.ios.monitor", category: "ActivityMonitor")

    override func intervalDidStart(for activity: DeviceActivityName) {
        super.intervalDidStart(for: activity)
        log.info("🟢 Monitoring interval started for \(activity.rawValue).")
        log.info("📱 DeviceActivity monitor extension is now active and listening for events")
    }

    override func intervalDidEnd(for activity: DeviceActivityName) {
        super.intervalDidEnd(for: activity)
        log.info("🔴 Monitoring interval ended for \(activity.rawValue).")
        log.info("📱 DeviceActivity monitor extension is now inactive")
    }

    // CRITICAL: Re-add event processing to capture real device activity
    override func eventDidReachThreshold(_ event: DeviceActivityEvent.Name, activity: DeviceActivityName) {
        super.eventDidReachThreshold(event, activity: activity)
        log.info("🎯 Activity event reached threshold: \(event.rawValue) for \(activity.rawValue)")

        // Create a simple activity record for the detected event
        // Fix: Capture values to avoid self capture in @Sendable closure
        let eventValue = event.rawValue
        let activityValue = activity.rawValue
        Task { @Sendable in
            await Self.logActivityEventStatic(event: eventValue, activity: activityValue)
        }
    }

    // Memory-safe activity logging for real device events
    private nonisolated func logActivityEvent(event: String, activity: String) async {
        await Self.logActivityEventStatic(event: event, activity: activity)
    }

    // Static method to avoid self capture in @Sendable closures
    private static func logActivityEventStatic(event: String, activity: String) async {
        let log = Logger(subsystem: "com.redpill.client.ios.monitor", category: "ActivityMonitor")
        let now = Date()

        // Create a real activity record for the detected device event
        let record = ActivityRecord(
            source: "device.activity.monitor",
            subtype: "threshold.reached",
            begin: Int64(now.timeIntervalSince1970 * 1000),
            end: Int64(now.timeIntervalSince1970 * 1000) + 1000, // 1 second event duration
            userTimezone: TimeZone.current.secondsFromGMT() / 60,
            workingIntensity: 1.0
        )

        // Use the shared ActivityStore to save the record
        // Access the MainActor-isolated store safely
        await ActivityStore.shared.append(contentsOf: [record])
        log.info("✅ Logged real device activity event: \(event) for activity: \(activity)")
    }
}
