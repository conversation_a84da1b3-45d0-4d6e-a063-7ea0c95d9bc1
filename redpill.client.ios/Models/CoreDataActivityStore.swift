//
//  CoreDataActivityStore.swift
//  redpill.client.ios
//
//  More efficient Core Data-based activity record persistence
//

import CoreData
import Foundation
import os.log
import SwiftUI

@MainActor
final class CoreDataActivityStore: ObservableObject {
    static let shared = CoreDataActivityStore()
    
    @Published private(set) var records: [ActivityRecord] = []
    @Published private(set) var recordCount: Int = 0
    
    private let log = Logger(subsystem: "com.redpill.client.ios", category: "CoreDataActivityStore")
    private let coreDataStack = CoreDataStack.shared
    
    private init() {
        Task {
            await loadRecentRecords()
        }
    }
    
    // MARK: - Public Interface
    
    nonisolated func append(contentsOf newRecords: [ActivityRecord]) async {
        guard !newRecords.isEmpty else { return }

        log.info("Saving \(newRecords.count) records to Core Data")

        let stack = coreDataStack
        await stack.performBackgroundTask { @Sendable context in
            for record in newRecords {
                let entity = ActivityRecordEntity(context: context)
                entity.id = record.id
                entity.source = record.source
                entity.subtype = record.subtype
                entity.begin = record.begin
                entity.end = record.end ?? 0
                entity.userTimezone = Int32(record.userTimezone)
                entity.workingIntensity = record.workingIntensity
                entity.submitted = false
            }

            do {
                try context.save()
                print("Successfully saved \(newRecords.count) records to Core Data")
            } catch {
                print("Failed to save records to Core Data: \(error.localizedDescription)")
            }
        }

        // Also save to legacy ActivityStore for extension compatibility
        await ActivityStore.shared.append(contentsOf: newRecords)

        // Update UI on main thread
        await MainActor.run {
            self.records.append(contentsOf: newRecords)
            self.recordCount += newRecords.count

            // Keep only recent records in memory
            if self.records.count > 1000 {
                self.records = Array(self.records.suffix(1000))
            }
        }
    }
    
    func loadRecentRecords() async {
        let fetchedRecords = await coreDataStack.performBackgroundTask { @Sendable context in
            let request: NSFetchRequest<ActivityRecordEntity> = ActivityRecordEntity.fetchRequest()
            request.sortDescriptors = [NSSortDescriptor(keyPath: \ActivityRecordEntity.begin, ascending: false)]
            request.fetchLimit = 1000

            do {
                let entities = try context.fetch(request)
                return entities.compactMap { entity in
                    ActivityRecord(
                        id: entity.id ?? UUID(),
                        source: entity.source ?? "",
                        subtype: entity.subtype ?? "",
                        begin: entity.begin,
                        end: entity.end == 0 ? nil : entity.end,
                        userTimezone: Int(entity.userTimezone),
                        workingIntensity: entity.workingIntensity
                    )
                }
            } catch {
                // Can't access self.log in Sendable closure, use print instead
                print("Failed to fetch records: \(error.localizedDescription)")
                return []
            }
        }
        
        await MainActor.run {
            self.records = fetchedRecords
            self.recordCount = fetchedRecords.count
            self.log.info("Loaded \(fetchedRecords.count) recent records from Core Data")
        }
    }
    
    // MARK: - Submission Support
    
    nonisolated func getUnsubmittedRecords() async -> [ActivityRecord] {
        let stack = coreDataStack
        return await stack.performBackgroundTask { @Sendable context in
            let request: NSFetchRequest<ActivityRecordEntity> = ActivityRecordEntity.fetchRequest()
            request.predicate = NSPredicate(format: "submitted == NO")
            request.sortDescriptors = [NSSortDescriptor(keyPath: \ActivityRecordEntity.begin, ascending: true)]

            do {
                let entities = try context.fetch(request)
                return entities.compactMap { entity in
                    ActivityRecord(
                        id: entity.id ?? UUID(),
                        source: entity.source ?? "",
                        subtype: entity.subtype ?? "",
                        begin: entity.begin,
                        end: entity.end == 0 ? nil : entity.end,
                        userTimezone: Int(entity.userTimezone),
                        workingIntensity: entity.workingIntensity
                    )
                }
            } catch {
                // Can't access self.log in Sendable closure, use print instead
                print("Failed to fetch unsubmitted records: \(error.localizedDescription)")
                return []
            }
        }
    }
    
    nonisolated func markRecordsAsSubmitted(_ recordIds: [UUID]) async {
        let stack = coreDataStack
        await stack.performBackgroundTask { @Sendable context in
            let request: NSFetchRequest<ActivityRecordEntity> = ActivityRecordEntity.fetchRequest()
            request.predicate = NSPredicate(format: "id IN %@", recordIds)

            do {
                let entities = try context.fetch(request)
                // Delete submitted records instead of just marking them
                for entity in entities {
                    context.delete(entity)
                }
                try context.save()
                print("Deleted \(entities.count) submitted records from Core Data")
            } catch {
                print("Failed to delete submitted records: \(error.localizedDescription)")
            }
        }

        // Also update the in-memory records array
        await MainActor.run {
            let recordIdsSet = Set(recordIds)
            self.records.removeAll { recordIdsSet.contains($0.id) }
            self.recordCount = self.records.count
        }
    }
    
    // MARK: - Debug Methods
    
    func addMockDataForUITesting() async {
        let now = Date()
        
        let mockApps = [
            ("debug_com.apple.mobilesafari", "Safari"),
            ("debug_com.apple.mobilemail", "Mail"),
            ("debug_com.apple.MobileSMS", "Messages"),
            ("debug_com.apple.Music", "Music"),
            ("debug_com.apple.camera", "Camera"),
            ("debug_com.apple.mobilenotes", "Notes"),
            ("debug_com.apple.weather", "Weather"),
            ("debug_com.apple.Maps", "Maps")
        ]
        
        var mockRecords: [ActivityRecord] = []
        
        for (bundleId, _) in mockApps {
            let sessionCount = Int.random(in: 3...5)
            
            for _ in 0..<sessionCount {
                let hoursAgo = Double.random(in: 0.5...8.0)
                let sessionDuration = Double.random(in: 30...600)
                
                let endTime = now.addingTimeInterval(-hoursAgo * 3600)
                let startTime = endTime.addingTimeInterval(-sessionDuration)
                
                let record = ActivityRecord(
                    source: bundleId,
                    subtype: "app.usage",
                    begin: Int64(startTime.timeIntervalSince1970 * 1000),
                    end: Int64(endTime.timeIntervalSince1970 * 1000),
                    userTimezone: TimeZone.current.secondsFromGMT() / 60,
                    workingIntensity: 1.0
                )
                
                mockRecords.append(record)
            }
        }
        
        mockRecords.sort { $0.begin < $1.begin }
        log.info("Generated \(mockRecords.count) mock activity records")
        await append(contentsOf: mockRecords)
    }
    
    nonisolated func logInAppInteraction() async {
        let now = Int64(Date().timeIntervalSince1970 * 1000)
        let tz = TimeZone.current.secondsFromGMT() / 60
        
        let record = ActivityRecord(
            source: "debug_pravaapp",
            subtype: "inapp.interaction",
            begin: now,
            end: now + 1000,
            userTimezone: tz,
            workingIntensity: 0.0
        )
        
        await append(contentsOf: [record])
    }
    
    func debugRecordCount() async {
        let stack = coreDataStack
        let totalCount = await stack.performBackgroundTask { @Sendable context in
            let request: NSFetchRequest<ActivityRecordEntity> = ActivityRecordEntity.fetchRequest()
            return (try? context.count(for: request)) ?? 0
        }

        let unsubmittedCount = await stack.performBackgroundTask { @Sendable context in
            let request: NSFetchRequest<ActivityRecordEntity> = ActivityRecordEntity.fetchRequest()
            request.predicate = NSPredicate(format: "submitted == NO")
            return (try? context.count(for: request)) ?? 0
        }

        await MainActor.run {
            log.info("Core Data: \(totalCount) total records, \(unsubmittedCount) unsubmitted")
            log.info("Memory: \(self.records.count) records loaded")
        }
    }

    // MARK: - Debug Tools for Local Storage Inspection

    /// Print detailed information about all records in Core Data
    func debugPrintAllRecords() async {
        let stack = coreDataStack
        let allRecords = await stack.performBackgroundTask { @Sendable context in
            let request: NSFetchRequest<ActivityRecordEntity> = ActivityRecordEntity.fetchRequest()
            request.sortDescriptors = [NSSortDescriptor(keyPath: \ActivityRecordEntity.begin, ascending: false)]
            request.fetchLimit = 50 // Limit to recent 50 records

            do {
                let entities = try context.fetch(request)
                return entities.map { entity in
                    (
                        id: entity.id?.uuidString ?? "nil",
                        source: entity.source ?? "nil",
                        subtype: entity.subtype ?? "nil",
                        begin: entity.begin,
                        end: entity.end,
                        submitted: entity.submitted
                    )
                }
            } catch {
                print("Failed to fetch records for debug: \(error.localizedDescription)")
                return []
            }
        }

        await MainActor.run {
            log.info("=== DEBUG: Recent 50 Core Data Records ===")
            for (index, record) in allRecords.enumerated() {
                let beginDate = Date(timeIntervalSince1970: TimeInterval(record.begin) / 1000)
                let endDate = record.end > 0 ? Date(timeIntervalSince1970: TimeInterval(record.end) / 1000) : nil
                let status = record.submitted ? "SUBMITTED" : "PENDING"

                log.info("[\(index + 1)] \(status) | \(record.source) | \(record.subtype) | \(beginDate) - \(endDate?.description ?? "ongoing")")
            }
            log.info("=== END DEBUG RECORDS ===")
        }
    }

    /// Get Core Data store file path for manual inspection
    func debugGetStoreFilePath() -> String? {
        return coreDataStack.persistentContainer.persistentStoreDescriptions.first?.url?.path
    }

    /// Export records to JSON file for inspection
    func debugExportRecordsToJSON() async -> String? {
        let records = await getUnsubmittedRecords()

        do {
            let jsonData = try JSONEncoder().encode(records)
            let documentsPath = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first!
            let jsonURL = documentsPath.appendingPathComponent("debug_records_export.json")

            try jsonData.write(to: jsonURL)

            await MainActor.run {
                log.info("Exported \(records.count) records to: \(jsonURL.path)")
            }

            return jsonURL.path
        } catch {
            await MainActor.run {
                log.error("Failed to export records: \(error.localizedDescription)")
            }
            return nil
        }
    }

    // Add missing methods for compatibility
    func load() async {
        await loadRecentRecords()
    }

    func fixFilePermissions() async {
        // For Core Data, we don't need file permissions, but we can recreate the database if needed
        log.info("Core Data doesn't require file permission fixes, but refreshing data...")
        await loadRecentRecords()
    }
}
