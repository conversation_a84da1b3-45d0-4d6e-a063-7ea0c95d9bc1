//
//  DashboardView.swift
//  redpill.client.ios
//

import SwiftUI

struct DashboardView: View {

    @EnvironmentObject private var focusViewModel: FocusViewModel
    @EnvironmentObject private var activityMonitor: ActivityMonitor
    @EnvironmentObject private var submissionService: SubmissionService

    @StateObject private var coreDataStore = CoreDataActivityStore.shared
    @StateObject private var eventCollector = ActivityEventCollector.shared
    @StateObject private var usageViewModel = UsageViewModel()

    @State private var tapCountForDebug = 0
    @State private var isDebugModeActive = false
    
    // State for the new simulation button's progress view
    @State private var isSimulating: Bool = false

    private static let debugTimestampFormatter: DateFormatter = {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd HH:mm:ss.SSS"
        formatter.timeZone = TimeZone.current
        return formatter
    }()

    private func formatTimestamp(_ timestamp: Int64) -> String {
        let date = Date(timeIntervalSince1970: TimeInterval(timestamp) / 1000.0)
        return Self.debugTimestampFormatter.string(from: date)
    }

    var body: some View {
        ScrollView {
            VStack(spacing: 20) {
                // ── Daily score ring ───────────────────────────────
                CircularProgressView(
                    currentValue: focusViewModel.dailyScore,
                    maxValue:     focusViewModel.dailyScoreMax,
                    lineWidth:    12,
                    showScoreText: true
                )
                .frame(width: 150, height: 150)
                .padding(.top, 20)
                .onTapGesture {
                    tapCountForDebug += 1
                    if tapCountForDebug >= 15 {
                        isDebugModeActive.toggle()
                        AppLogger.view.info("Debug mode toggled: \(self.isDebugModeActive ? "ON" : "OFF")")
                        tapCountForDebug = 0
                    }
                }

                lastSubmissionView

                // ── Main Action Buttons ────────────────────────────────
                mainActionButtons

                // ── Activity Records List ────────────────────────────────
                activityRecordsList

                // ── Debug console (hidden by default) ────────────────────────────────
                if isDebugModeActive {
                    debugConsole
                }
            }
            .padding()
        }
        .onAppear {
            AppLogger.view.info("DashboardView appeared.")
            activityMonitor.currentViewChanged(to: "DashboardView")
        }
        .navigationTitle("Dashboard")
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(.systemGroupedBackground))
    }

    // MARK: - Main Action Buttons
    @ViewBuilder
    private var mainActionButtons: some View {
        VStack(spacing: 16) {
            // Submit to Backend Button
            Button {
                submissionService.triggerManualSubmission()
            } label: {
                HStack {
                    Image(systemName: "icloud.and.arrow.up")
                        .font(.title2)
                    if submissionService.isSubmitting {
                        Text("Submitting...")
                        ProgressView()
                            .scaleEffect(0.8)
                    } else {
                        Text("Submit to Backend")
                    }
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 16)
                .background(Color.blue)
                .foregroundColor(.white)
                .cornerRadius(12)
                .font(.headline)
            }
            .disabled(submissionService.isSubmitting)

            // Debug Actions (only when debug mode is active)
            if isDebugModeActive {
                VStack(spacing: 8) {
                    HStack(spacing: 12) {
                        Button("Generate Events") {
                            Task {
                                await eventCollector.generateTestEvents()
                                await coreDataStore.load()
                            }
                        }
                        .buttonStyle(.borderedProminent)
                        .tint(.green)

                        Button("Log Interaction") {
                            Task {
                                await eventCollector.logUserInteraction("tap", in: "dashboard")
                                await coreDataStore.load()
                            }
                        }
                        .buttonStyle(.bordered)
                    }

                    // Test DeviceActivity monitoring
                    Button("Test Monitoring") {
                        activityMonitor.testMonitoringStatus()
                    }
                    .buttonStyle(.bordered)
                    .tint(.purple)

                    // Test report extension
                    Button("Test Report") {
                        Task {
                            AppLogger.view.info("🧪 Manually testing DeviceActivity report extension")
                            // This should trigger the report extension
                            await MainActor.run {
                                // Force a view update that should trigger the report
                                if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene {
                                    AppLogger.view.info("📱 Found window scene, triggering report refresh")
                                }
                            }
                        }
                    }
                    .buttonStyle(.bordered)
                    .tint(.cyan)
                }
            }
        }
    }

    // MARK: - Activity Records List
    @ViewBuilder
    private var activityRecordsList: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Header
            HStack {
                Text("Local Activity Records")
                    .font(.headline)
                    .fontWeight(.semibold)

                Spacer()

                let totalRecords = coreDataStore.records.count
                Text("\(totalRecords) records")
                    .font(.subheadline)
                    .foregroundColor(.secondary)
            }

            // Records List
            if coreDataStore.records.count > 0 {
                List {
                    ForEach(coreDataStore.records.prefix(15)) { record in
                        recordRowView(record: record, source: "Core Data")
                    }
                }
                .frame(maxHeight: 300)
                .listStyle(.plain)
            } else {
                Text("No activity records found")
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity, alignment: .center)
                    .padding()
            }
        }
        .padding()
        .background(Color(.secondarySystemBackground))
        .cornerRadius(12)
    }

    // MARK: - Debug Console
    @ViewBuilder
    private var debugConsole: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Debug Console")
                .font(.headline)
                .fontWeight(.semibold)

            Text("Today's Usage: \(usageViewModel.todayMinutes) minutes")
                .font(.subheadline)
                .foregroundColor(.secondary)

            VStack(spacing: 8) {
                HStack(spacing: 8) {
                    Button("Debug Store") {
                        Task {
                            await coreDataStore.debugRecordCount()
                        }
                    }
                    .buttonStyle(.bordered)

                    Button("Refresh Data") {
                        Task {
                            await coreDataStore.load()
                            await eventCollector.logUserInteraction("refresh", in: "debug")
                        }
                    }
                    .buttonStyle(.bordered)
                }

                HStack(spacing: 8) {
                    Button("Print Records") {
                        Task {
                            await coreDataStore.debugPrintAllRecords()
                        }
                    }
                    .buttonStyle(.bordered)
                    .tint(.orange)

                    Button("Export JSON") {
                        Task {
                            if let path = await coreDataStore.debugExportRecordsToJSON() {
                                AppLogger.view.info("Records exported to: \(path)")
                            }
                        }
                    }
                    .buttonStyle(.bordered)
                    .tint(.purple)
                }

                HStack(spacing: 8) {
                    Button("Cleanup Submitted") {
                        Task {
                            await coreDataStore.debugCleanupSubmittedRecords()
                            await coreDataStore.load()
                        }
                    }
                    .buttonStyle(.bordered)
                    .tint(.red)

                    Button("Force Report Refresh") {
                        Task {
                            // Force trigger the report extension
                            await MainActor.run {
                                if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
                                   let window = windowScene.windows.first,
                                   let rootViewController = window.rootViewController {
                                    // This will trigger a report refresh
                                    rootViewController.view.setNeedsLayout()
                                }
                            }
                        }
                    }
                    .buttonStyle(.bordered)
                    .tint(.blue)
                }

                if let storePath = coreDataStore.debugGetStoreFilePath() {
                    Text("Core Data Store: \(storePath)")
                        .font(.caption2)
                        .foregroundColor(.secondary)
                        .multilineTextAlignment(.leading)
                }
            }
        }
        .padding()
        .background(Color(.tertiarySystemBackground))
        .cornerRadius(12)
    }

    // MARK: - Helper Views and Methods

    /// A view that displays the last successful submission timestamp.
    @ViewBuilder
    private var lastSubmissionView: some View {
        Group {
            if let date = submissionService.lastSuccessfulSubmission {
                Text("Last sync: \(date.formatted(date: .abbreviated, time: .shortened))")
            } else {
                Text("Last sync: Never")
            }
        }
        .font(.caption)
        .foregroundColor(.secondary)
        .padding(.bottom, 10)
    }

    private func formatDuration(start: Int64, end: Int64?) -> String {
        guard let end = end else { return "ongoing" }
        let durationSeconds = Double(end - start) / 1000.0
        let minutes = Int(durationSeconds / 60)
        let seconds = Int(durationSeconds.truncatingRemainder(dividingBy: 60))
        return "\(minutes)m \(seconds)s"
    }



    @ViewBuilder
    private func recordRowView(record: ActivityRecord, source: String) -> some View {
        VStack(alignment: .leading, spacing: 4) {
            HStack {
                Text(record.source)
                    .fontWeight(.bold)
                    .foregroundColor(record.source.hasPrefix("debug_") ? .orange : .primary)
                Spacer()
                Text(source)
                    .font(.caption)
                    .foregroundColor(source == "Core Data" ? .green : .blue)
            }

            HStack {
                Text("Duration: \(formatDuration(start: record.begin, end: record.end))")
                    .font(.caption)
                    .foregroundColor(.secondary)
                Spacer()
                Text(formatTimestamp(record.begin))
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding(.vertical, 6)
        .padding(.horizontal, 8)
        .background(Color(.tertiarySystemBackground))
        .cornerRadius(8)
    }
}
