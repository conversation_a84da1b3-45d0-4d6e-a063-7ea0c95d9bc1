//
//  ActivityMonitor.swift
//  redpill.client.ios
//
//  Centralised wrapper around Device<PERSON>ctivityCenter that tracks high-level
//  app events (view navigation, foreground/background) and schedules
//  system-level usage monitoring.
//
//  Member of **all three targets** so both the extensions and the host
//  app can post view-change breadcrumbs.
//

@preconcurrency import DeviceActivity         // silences non-Sendable warnings while Apple audits API
import Foundation
import FamilyControls
import os.log
import SwiftUI                                 // needed for @Published
import Darwin.Mach                             // for memory monitoring

@MainActor
final class ActivityMonitor: ObservableObject {

    // MARK: – Public, observable state
    /// Last screen or interaction recorded by the host app.
    @Published var currentView: String = "launch"

    // MARK: – Private
    private let log    = Logger(subsystem: "com.redpill.client.ios", category: "ActivityMonitor")
    private let center = DeviceActivityCenter()

    /// Stable identifiers required by DeviceActivity APIs
    private static let activityName = DeviceActivityName("global.usage.monitoring")
    private static let eventName    = DeviceActivityEvent.Name("global.usage.monitoring.event")

    //  ActivityMonitor.swift  – replace the whole startMonitoring() body
    func startMonitoring() async {
        log.info("Attempting to start monitoring…")

        let schedule = DeviceActivitySchedule(
            intervalStart: DateComponents(hour: 0, minute: 0),
            intervalEnd:   DateComponents(hour: 23, minute: 59),
            repeats:       true,
            warningTime:   nil)

        // Create an extremely sensitive threshold to capture app switches
        let event = DeviceActivityEvent(
            applications: [],  // Empty means "all applications"
            categories:   [],  // Empty means "all categories"
            webDomains:   [],  // Empty means "all web domains"
            threshold:    DateComponents(second: 1))  // 1-second threshold for immediate detection

        do {
            try center.startMonitoring(
                Self.activityName,
                during: schedule,
                events: [Self.eventName : event])
            log.info("✅ Device-activity monitoring started with 1-second threshold.")
            log.info("📊 Monitoring schedule configured")
            log.info("🎯 Event configuration: threshold=1s, all apps/categories/domains")
        } catch {
            log.error("🔴 Failed to start monitoring: \(error.localizedDescription)")
        }
    }



    func stopMonitoring() {
        log.info("Stopping monitoring.")
        center.stopMonitoring([Self.activityName])
    }

    /// Force refresh monitoring to capture latest data with memory safety
    func refreshMonitoring() async {
        log.info("Refreshing monitoring to capture latest data...")

        // Check memory pressure before proceeding
        if await isMemoryPressureHigh() {
            log.warning("High memory pressure detected, skipping refresh to prevent crash")
            return
        }

        // Stop current monitoring
        center.stopMonitoring([Self.activityName])

        // Wait a moment
        try? await Task.sleep(nanoseconds: 500_000_000) // 0.5 seconds

        // Restart monitoring
        await startMonitoring()

        // Also trigger a manual data collection as fallback
        await collectCurrentActivityData()
    }

    /// Check if memory pressure is high to prevent crashes
    private func isMemoryPressureHigh() async -> Bool {
        // Get current memory usage
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4

        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_,
                         task_flavor_t(MACH_TASK_BASIC_INFO),
                         $0,
                         &count)
            }
        }

        if kerr == KERN_SUCCESS {
            let memoryUsage = info.resident_size
            // Different limits: 16MB for extensions, 100MB for main app
            #if MONITOR_EXTENSION
            let memoryLimit: UInt64 = 16 * 1024 * 1024 // 16MB for extensions
            #else
            let memoryLimit: UInt64 = 100 * 1024 * 1024 // 100MB for main app
            #endif

            if memoryUsage > memoryLimit {
                log.warning("Memory usage (\(memoryUsage / 1024 / 1024)MB) exceeds safe limit (\(memoryLimit / 1024 / 1024)MB)")
                return true
            }
        }

        return false
    }

    /// Manual data collection as fallback when DeviceActivity isn't working
    private func collectCurrentActivityData() async {
        log.info("Collecting current activity data manually...")

        // Create a simple activity record for the current app usage
        let now = Date()
        let record = ActivityRecord(
            source: "com.redpill.client.ios",
            subtype: "app.session",
            begin: Int64(now.timeIntervalSince1970 * 1000),
            end: nil, // Ongoing session
            userTimezone: TimeZone.current.secondsFromGMT() / 60,
            workingIntensity: 1.0
        )

        await ActivityStore.shared.append(contentsOf: [record])
        log.info("Added manual activity record")
    }

    // MARK: – High-level app breadcrumbs
    /// Call from host-app views to note navigation or UI events.
    func currentViewChanged(to viewIdentifier: String) {
        currentView = viewIdentifier
        log.debug("View changed ➜ \(viewIdentifier, privacy: .public)")
    }

    // Optional helpers exposed to the `redpill_client_iosApp`
    func appDidBecomeActive()  {
        log.info("App became active.")
        // Log current monitoring status
        log.info("📱 App activation - checking if monitoring is active")
    }

    func appDidEnterBackground() {
        log.info("App entered background.")
        // This could trigger activity detection if the user switches to another app
        log.info("📱 App backgrounded - user may be switching to another app")
    }

    // Test method to verify monitoring is working
    func testMonitoringStatus() {
        log.info("🔍 Testing monitoring status...")
        log.info("📊 Activity name: \(Self.activityName.rawValue)")
        log.info("🎯 Event name: \(Self.eventName.rawValue)")

        // Check authorization status
        let authStatus = AuthorizationCenter.shared.authorizationStatus
        let statusDescription = switch authStatus {
        case .notDetermined: "Not Determined"
        case .denied: "Denied"
        case .approved: "Approved"
        @unknown default: "Unknown (\(authStatus.rawValue))"
        }
        log.info("🔐 Authorization status: \(statusDescription)")

        if authStatus != .approved {
            log.error("❌ DeviceActivity monitoring requires Screen Time permission")
            log.error("❌ Go to Settings > Screen Time > App & Website Activity")
            return
        }

        log.info("✅ Authorization approved, monitoring should be active")
        log.info("📱 To test: Use other apps for 1+ seconds, then return to see if events are captured")

        // Create a test activity record to verify the store is working
        Task {
            await collectCurrentActivityData()
        }
    }

    // Test method to check if DeviceActivity reports are working
    func testDeviceActivityReports() async {
        log.info("🧪 Testing DeviceActivity report system...")

        // Check if we can access DeviceActivity data
        let today = Calendar.current.dateInterval(of: .day, for: .now)!
        let filter = DeviceActivityFilter(
            segment: .daily(during: today),
            users: .all,
            devices: .init([.iPhone])
        )

        log.info("📊 Created filter for today: \(today)")
        log.info("🔄 Report extensions should be triggered automatically when app usage occurs")
        log.info("💡 Try switching to Safari, Messages, or other apps for 30+ seconds")
    }

    // Comprehensive DeviceActivity diagnostics
    func runDeviceActivityDiagnostics() async {
        log.info("🔍 === DeviceActivity Comprehensive Diagnostics ===")

        // 1. Check authorization
        let authStatus = AuthorizationCenter.shared.authorizationStatus
        let statusDescription = switch authStatus {
        case .notDetermined: "Not Determined"
        case .denied: "Denied"
        case .approved: "Approved"
        @unknown default: "Unknown (\(authStatus.rawValue))"
        }
        log.info("🔐 Authorization Status: \(statusDescription)")

        // 2. Check if monitoring is active
        log.info("📊 Monitoring Configuration:")
        log.info("   - Activity Name: \(Self.activityName.rawValue)")
        log.info("   - Event Name: \(Self.eventName.rawValue)")
        log.info("   - Threshold: 1 second")
        log.info("   - Schedule: 00:00 - 23:59 daily")

        // 3. Check app group access
        if let containerURL = FileManager.default.containerURL(forSecurityApplicationGroupIdentifier: PravaIDs.appGroup) {
            log.info("📁 App Group Container: \(containerURL.path)")
            let isWritable = FileManager.default.isWritableFile(atPath: containerURL.path)
            log.info("📁 Container Writable: \(isWritable)")
        } else {
            log.error("❌ App Group Container NOT ACCESSIBLE")
        }

        // 4. Check bundle identifiers
        let mainBundleId = Bundle.main.bundleIdentifier ?? "unknown"
        log.info("📱 Main App Bundle ID: \(mainBundleId)")

        // 5. Test DeviceActivity center
        do {
            // Try to stop and restart monitoring to test if it's working
            center.stopMonitoring([Self.activityName])
            log.info("✅ Successfully stopped monitoring (DeviceActivityCenter accessible)")

            // Restart monitoring
            await startMonitoring()

        } catch {
            log.error("❌ DeviceActivityCenter error: \(error.localizedDescription)")
        }

        // 6. Check for common issues
        log.info("🔍 Common Issues Check:")
        log.info("   - Screen Time enabled in Settings? (Required)")
        log.info("   - App & Website Activity enabled? (Required)")
        log.info("   - Running on physical device? (Required - Simulator won't work)")
        log.info("   - iOS 15+ ? (Required)")

        log.info("🔍 === End Diagnostics ===")
    }

    // Force trigger monitoring by restarting it
    func forceRestartMonitoring() async {
        log.info("🔄 Force restarting DeviceActivity monitoring...")

        // Stop current monitoring
        center.stopMonitoring([Self.activityName])
        log.info("⏹️ Stopped current monitoring")

        // Wait a moment
        try? await Task.sleep(nanoseconds: 1_000_000_000) // 1 second

        // Restart monitoring
        await startMonitoring()
        log.info("▶️ Restarted monitoring - this should trigger fresh event detection")
    }

    // Test if we can manually trigger an event
    func simulateAppSwitch() async {
        log.info("🧪 Simulating app switch detection...")

        // Create a test record to verify the system is working
        let now = Date()
        let testRecord = ActivityRecord(
            source: "test.app.switch",
            subtype: "manual.trigger",
            begin: Int64(now.timeIntervalSince1970 * 1000),
            end: Int64(now.timeIntervalSince1970 * 1000) + 5000, // 5 seconds
            userTimezone: TimeZone.current.secondsFromGMT() / 60,
            workingIntensity: 1.0
        )

        await ActivityStore.shared.append(contentsOf: [testRecord])
        log.info("✅ Created manual test record - check if it appears in the dashboard")
    }
}
